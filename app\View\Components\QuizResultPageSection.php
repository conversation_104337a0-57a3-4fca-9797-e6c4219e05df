<?php

namespace App\View\Components;

use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;
use Modules\Certificate\Entities\Certificate;
use Modules\Quiz\Entities\OnlineQuiz;
use Modules\Quiz\Entities\QuizeSetup;
use Modules\Quiz\Entities\QuizTest;

class QuizResultPageSection extends Component
{
    public $quiz, $user, $course;

    public function __construct($quiz, $user, $course)
    {
        $this->quiz = $quiz;
        $this->user = $user;
        $this->course = $course;
    }


    public function render()
    {
        $quiz = $this->quiz;
        $quizSetup = QuizeSetup::getData();
        $user = Auth::user();
        $all = QuizTest::with([
            'quiz.subCategory',
            'quiz.group',
            'details.question.questionGroup',
            'details.question.questionLevel',
            'details.question.subCategory'
        ])->where('course_id', $this->course->id)->where('user_id', $user->id)->get();
        $result = [];
        $preResult = [];
        $onlineQuiz = OnlineQuiz::with('assign.questionBank.questionGroup')->find($this->quiz->quiz_id);

        // Difficulty breakdown by Question Group (count of questions per group in this quiz)
        $difficultyByGroup = [];
        if ($onlineQuiz) {
            foreach ($onlineQuiz->assign as $assign) {
                $gTitle = optional($assign->questionBank->questionGroup)->title ?: 'Unknown';
                $difficultyByGroup[$gTitle]['total'] = ($difficultyByGroup[$gTitle]['total'] ?? 0) + 1;
            }
        }
        if (!empty($difficultyByGroup)) {
            $result['difficultyBreakdown'] = $difficultyByGroup;
        }

        // Always compute Subject & Difficulty breakdowns for the current attempt
        $current = QuizTest::with(['quiz.subCategory','quiz.group','details.question.questionGroup','details.question.questionLevel','details.question.subCategory'])->find($quiz->id);
        if ($current) {
            // New logic: Difficulty purely by Question Group title (count of answered questions in this attempt)
            $byDifficulty = [];
            $bySubject = [];
            foreach ($current->details as $d) {
                $isCorrect = $d->status == 1 ? 1 : 0;

                // Difficulty by per-question group title
                $gTitle = optional($d->question->questionGroup)->title ?: 'Unknown';
                $byDifficulty[$gTitle]['total'] = ($byDifficulty[$gTitle]['total'] ?? 0) + 1;

                // Subject by per-question subcategory
                $subjectName = optional($d->question->subCategory)->name
                    ?: (optional($current->quiz->subCategory)->name ?: 'Uncategorized');
                $bySubject[$subjectName]['total'] = ($bySubject[$subjectName]['total'] ?? 0) + 1;
                $bySubject[$subjectName]['correct'] = ($bySubject[$subjectName]['correct'] ?? 0) + $isCorrect;
            }
            foreach ($bySubject as $k => $v) {
                $t = $v['total'] ?? 0; $c = $v['correct'] ?? 0;
                $bySubject[$k]['wrong'] = max(0, $t - $c);
                $bySubject[$k]['pct'] = $t ? round(100 * $c / $t) : 0;
            }
            $result['difficultyBreakdown'] = $byDifficulty;
            $result['subjectBreakdown'] = $bySubject;
        }

        foreach ($all as $key => $i) {


            $date = showDate($i->created_at);
            $totalQus = totalQuizQus($i->quiz_id);
            $totalAns = count($i->details);
            $totalCorrect = 0;
            $totalScore = totalQuizMarks($i->quiz_id);
            $score = 0;
            if ($totalAns != 0) {
                foreach ($i->details as $test) {

                    if ($test->status == 1) {
                        $score += $test->mark ?? 1;
                        $totalCorrect++;
                    }

                }
            }

            if ($quiz->id == $i->id) {
                $result['start_at'] = $i->start_at;
                $result['end_at'] = $i->end_at;
                $result['duration'] = $i->duration;
                $result['totalQus'] = $totalQus;
                $result['totalAns'] = $totalAns;
                $result['totalCorrect'] = $totalCorrect;
                $result['totalWrong'] = $totalAns - $totalCorrect;
                $result['score'] = $score;
                $result['totalScore'] = $totalScore;
                $result['passMark'] = $onlineQuiz->percentage ?? 0;
                $result['mark'] = $score > 0 ? round($score / $totalScore * 100, 2) : 0;;

                // New: rebuild Difficulty and Subject from per-question data
                $byDifficulty = [];
                $bySubject = [];
                foreach ($i->details as $d) {
                    $isCorrect = $d->status == 1 ? 1 : 0;
                    $subjectName = optional($d->question->subCategory)->name
                        ?: (optional($i->quiz->subCategory)->name ?: 'Uncategorized');
                    $bySubject[$subjectName]['total'] = ($bySubject[$subjectName]['total'] ?? 0) + 1;
                    $bySubject[$subjectName]['correct'] = ($bySubject[$subjectName]['correct'] ?? 0) + $isCorrect;

                    $diffName = optional($d->question->questionLevel)->title
                        ?: (optional($d->question->questionGroup)->title ?: 'Unknown');
                    $byDifficulty[$diffName]['total'] = ($byDifficulty[$diffName]['total'] ?? 0) + 1;
                    $byDifficulty[$diffName]['correct'] = ($byDifficulty[$diffName]['correct'] ?? 0) + $isCorrect;
                }
                foreach ($byDifficulty as $k => $v) {
                    $t = $v['total'] ?? 0; $c = $v['correct'] ?? 0;
                    $byDifficulty[$k]['wrong'] = max(0, $t - $c);
                    $byDifficulty[$k]['pct'] = $t ? round(100 * $c / $t) : 0;
                }
                foreach ($bySubject as $k => $v) {
                    $t = $v['total'] ?? 0; $c = $v['correct'] ?? 0;
                    $bySubject[$k]['wrong'] = max(0, $t - $c);
                    $bySubject[$k]['pct'] = $t ? round(100 * $c / $t) : 0;
                }

                $result['difficultyBreakdown'] = $byDifficulty;
                $result['subjectBreakdown'] = $bySubject;


                if (isModuleActive('AdvanceQuiz')) {
                    $isPass = ($result['mark'] >= $result['passMark']) && ($i->flag != 1);
                } else {
                    $isPass = $result['mark'] >= $result['passMark'];
                }
                $result['pass'] = $isPass ? 1 : 0;
                $result['status'] = $isPass ? trans('quiz.Passed') : trans('quiz.Failed');
                $result['text_color'] = $isPass ? "success_text" : "error_text";
                $i->pass = $isPass ? 1 : 0;

                $i->save();
            } else {
                $preResult[$key]['quiz_test_id'] = $i->id;
                $preResult[$key]['totalQus'] = $totalQus;
                $preResult[$key]['date'] = $date;
                $preResult[$key]['totalAns'] = $totalAns;
                $preResult[$key]['totalCorrect'] = $totalCorrect;
                $preResult[$key]['totalWrong'] = $totalAns - $totalCorrect;
                $preResult[$key]['score'] = $score;
                $preResult[$key]['totalScore'] = $totalScore;
                $preResult[$key]['passMark'] = $onlineQuiz->percentage ?? 0;
                $preResult[$key]['mark'] = $score > 0 ? round($score / $totalScore * 100, 2) : 0;
                if (isModuleActive('AdvanceQuiz')) {
                    $isPass = ($preResult[$key]['mark'] >= $preResult[$key]['passMark']) && ($i->flag != 1);
                } else {
                    $isPass = $preResult[$key]['mark'] >= $preResult[$key]['passMark'];
                }
                $preResult[$key]['pass'] = $isPass ? 1 : 0;
                $preResult[$key]['status'] = $isPass ? trans('quiz.Passed') : trans('quiz.Failed');
                $preResult[$key]['text_color'] = $isPass ? "success_text" : "error_text";
//                $i->pass = $preResult[$key]['mark'] >= $isPass ? 1 : 0;
//                $i->save();
            }

        }

        // Fallback: ensure difficulty/subject breakdowns exist for the current attempt
        if (empty($result['difficultyBreakdown']) || empty($result['subjectBreakdown'])) {
            $current = QuizTest::with(['quiz.subCategory','quiz.group','details.question.questionGroup','details.question.questionLevel','details.question.subCategory'])->find($quiz->id);
            if ($current) {
                $byDifficulty = [];
                $bySubject = [];
                foreach ($current->details as $d) {
                    $isCorrect = $d->status == 1 ? 1 : 0;
                    $subjectName = optional($d->question->subCategory)->name
                        ?: (optional($current->quiz->subCategory)->name ?: 'Uncategorized');
                    $bySubject[$subjectName]['total'] = ($bySubject[$subjectName]['total'] ?? 0) + 1;
                    $bySubject[$subjectName]['correct'] = ($bySubject[$subjectName]['correct'] ?? 0) + $isCorrect;

                    $diffName = optional($d->question->questionLevel)->title
                        ?: (optional($d->question->questionGroup)->title ?: 'Unknown');

        // Ensure non-empty breakdowns if answers exist but mapping was missing
        if ((($result['totalAns'] ?? 0) > 0) && empty($result['subjectBreakdown'])) {
            $ta = (int)($result['totalAns'] ?? 0);
            $tc = (int)($result['totalCorrect'] ?? 0);
            $tw = max(0, $ta - $tc);
            $result['subjectBreakdown'] = [
                'Uncategorized' => [
                    'total' => $ta,
                    'correct' => $tc,
                    'wrong' => $tw,
                    'pct' => $ta ? round(100 * $tc / $ta) : 0,
                ]
            ];
        }
        if ((($result['totalAns'] ?? 0) > 0) && empty($result['difficultyBreakdown'])) {
            $ta = (int)($result['totalAns'] ?? 0);
            $tc = (int)($result['totalCorrect'] ?? 0);
            $tw = max(0, $ta - $tc);
            $result['difficultyBreakdown'] = [
                'Unknown' => [
                    'total' => $ta,
                    'correct' => $tc,
                    'wrong' => $tw,
                    'pct' => $ta ? round(100 * $tc / $ta) : 0,
                ]
            ];
        }

                    $byDifficulty[$diffName]['total'] = ($byDifficulty[$diffName]['total'] ?? 0) + 1;
                    $byDifficulty[$diffName]['correct'] = ($byDifficulty[$diffName]['correct'] ?? 0) + $isCorrect;
                }
                foreach ($byDifficulty as $k => $v) {
                    $t = $v['total'] ?? 0; $c = $v['correct'] ?? 0;
                    $byDifficulty[$k]['wrong'] = max(0, $t - $c);
                    $byDifficulty[$k]['pct'] = $t ? round(100 * $c / $t) : 0;
                }
                foreach ($bySubject as $k => $v) {
                    $t = $v['total'] ?? 0; $c = $v['correct'] ?? 0;
                    $bySubject[$k]['wrong'] = max(0, $t - $c);
                    $bySubject[$k]['pct'] = $t ? round(100 * $c / $t) : 0;
                }
                $result['difficultyBreakdown'] = $byDifficulty;
                $result['subjectBreakdown'] = $bySubject;
            }
        }


        $certificate = Certificate::where('for_quiz', 1)->first();
        return view(theme('components.quiz-result-page-section'), compact('onlineQuiz', 'certificate', 'quizSetup', 'result', 'preResult'));
    }
}
