<?php
// Simple script to create test quiz result data
// Run this from the Laravel root directory: php create_test_quiz_result.php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Modules\Quiz\Entities\QuizTest;
use Modules\Quiz\Entities\QuizTestDetails;
use Modules\Quiz\Entities\OnlineQuiz;
use Modules\Quiz\Entities\QuestionBank;
use Modules\CourseSetting\Entities\Course;
use App\User;

try {
    // Find or create a test user
    $user = User::first();
    if (!$user) {
        echo "No users found. Please create a user first.\n";
        exit;
    }

    // Find a quiz course
    $course = Course::where('type', 2)->first(); // type 2 = quiz
    if (!$course) {
        echo "No quiz courses found. Please create a quiz course first.\n";
        exit;
    }

    // Find the online quiz
    $onlineQuiz = OnlineQuiz::first();
    if (!$onlineQuiz) {
        echo "No online quizzes found. Please run the quiz seeders first.\n";
        exit;
    }

    // Create a quiz test
    $quizTest = QuizTest::create([
        'user_id' => $user->id,
        'course_id' => $course->id,
        'quiz_id' => $onlineQuiz->id,
        'publish' => 1,
        'start_at' => now(),
        'end_at' => now()->addMinutes(30),
    ]);

    echo "Created QuizTest with ID: {$quizTest->id}\n";

    // Get questions from different groups
    $questions = QuestionBank::whereIn('q_group_id', [1, 2, 3])->take(10)->get();
    
    if ($questions->isEmpty()) {
        echo "No questions found. Please run the quiz seeders first.\n";
        exit;
    }

    // Create quiz test details for each question
    foreach ($questions as $index => $question) {
        $isCorrect = rand(0, 1); // Random correct/incorrect
        
        QuizTestDetails::create([
            'quiz_test_id' => $quizTest->id,
            'qus_id' => $question->id,
            'status' => $isCorrect,
            'mark' => $isCorrect ? $question->marks : 0,
        ]);
        
        echo "Added question {$question->id} from group {$question->q_group_id} - " . ($isCorrect ? 'Correct' : 'Incorrect') . "\n";
    }

    echo "\nTest quiz result created successfully!\n";
    echo "You can now view it at: /quizResult/{$quizTest->id}\n";
    echo "User ID: {$user->id}\n";
    echo "Course ID: {$course->id}\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
