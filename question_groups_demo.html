<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question Groups Visualization Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .question-groups-section {
            margin: 3rem 0;
        }

        .section-header h3 {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            text-align: center;
        }

        .group-stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 1.5rem;
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            margin-bottom: 1rem;
        }

        .group-stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            height: 100%;
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .progress {
            height: 6px;
            background-color: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .progress-bar {
            background: linear-gradient(90deg, #2ecc71, #27ae60);
            border-radius: 3px;
            transition: width 0.8s ease;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="question-groups-section">
            <div class="section-header text-center mb-4">
                <h3>
                    <i class="fas fa-layer-group" style="color: #3498db; margin-right: 0.5rem;"></i>
                    Question Groups Analysis
                </h3>
                <p style="color: #7f8c8d; font-size: 1rem; margin: 0;">Detailed breakdown of your performance across different question categories</p>
            </div>

            <!-- Question Groups Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-4 col-sm-6 mb-3">
                    <div class="group-stat-card">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 style="margin: 0; font-weight: 600; font-size: 1.1rem;">PHP Basics</h5>
                            <i class="fas fa-chart-pie" style="font-size: 1.2rem; opacity: 0.8;"></i>
                        </div>
                        <div class="group-stats">
                            <div class="stat-row d-flex justify-content-between mb-1">
                                <span style="font-size: 0.9rem; opacity: 0.9;">Total Questions:</span>
                                <strong style="font-size: 1rem;">8</strong>
                            </div>
                            <div class="stat-row d-flex justify-content-between mb-1">
                                <span style="font-size: 0.9rem; opacity: 0.9;">Correct:</span>
                                <strong style="font-size: 1rem; color: #2ecc71;">6</strong>
                            </div>
                            <div class="stat-row d-flex justify-content-between mb-2">
                                <span style="font-size: 0.9rem; opacity: 0.9;">Accuracy:</span>
                                <strong style="font-size: 1rem;">75.0%</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" style="width: 75%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-6 mb-3">
                    <div class="group-stat-card">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 style="margin: 0; font-weight: 600; font-size: 1.1rem;">Advanced PHP</h5>
                            <i class="fas fa-chart-pie" style="font-size: 1.2rem; opacity: 0.8;"></i>
                        </div>
                        <div class="group-stats">
                            <div class="stat-row d-flex justify-content-between mb-1">
                                <span style="font-size: 0.9rem; opacity: 0.9;">Total Questions:</span>
                                <strong style="font-size: 1rem;">5</strong>
                            </div>
                            <div class="stat-row d-flex justify-content-between mb-1">
                                <span style="font-size: 0.9rem; opacity: 0.9;">Correct:</span>
                                <strong style="font-size: 1rem; color: #2ecc71;">3</strong>
                            </div>
                            <div class="stat-row d-flex justify-content-between mb-2">
                                <span style="font-size: 0.9rem; opacity: 0.9;">Accuracy:</span>
                                <strong style="font-size: 1rem;">60.0%</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" style="width: 60%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-6 mb-3">
                    <div class="group-stat-card">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 style="margin: 0; font-weight: 600; font-size: 1.1rem;">Database</h5>
                            <i class="fas fa-chart-pie" style="font-size: 1.2rem; opacity: 0.8;"></i>
                        </div>
                        <div class="group-stats">
                            <div class="stat-row d-flex justify-content-between mb-1">
                                <span style="font-size: 0.9rem; opacity: 0.9;">Total Questions:</span>
                                <strong style="font-size: 1rem;">7</strong>
                            </div>
                            <div class="stat-row d-flex justify-content-between mb-1">
                                <span style="font-size: 0.9rem; opacity: 0.9;">Correct:</span>
                                <strong style="font-size: 1rem; color: #2ecc71;">5</strong>
                            </div>
                            <div class="stat-row d-flex justify-content-between mb-2">
                                <span style="font-size: 0.9rem; opacity: 0.9;">Accuracy:</span>
                                <strong style="font-size: 1rem;">71.4%</strong>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" style="width: 71.4%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Charts Row -->
            <div class="row">
                <!-- Question Groups Distribution (Doughnut Chart) -->
                <div class="col-lg-6 mb-4">
                    <div class="chart-card">
                        <h4 style="text-align:center; margin-bottom: 1.5rem; color:#2c3e50; font-weight:600;">
                            <i class="fas fa-chart-pie" style="color: #e74c3c; margin-right: 0.5rem;"></i>
                            Questions Distribution
                        </h4>
                        <div class="chart-container">
                            <canvas id="questionGroupsPie"></canvas>
                        </div>
                        <div class="chart-legend mt-3" id="pieChartLegend" style="text-align: center;"></div>
                    </div>
                </div>

                <!-- Performance Comparison (Bar Chart) -->
                <div class="col-lg-6 mb-4">
                    <div class="chart-card">
                        <h4 style="text-align:center; margin-bottom: 1.5rem; color:#2c3e50; font-weight:600;">
                            <i class="fas fa-chart-bar" style="color: #3498db; margin-right: 0.5rem;"></i>
                            Performance by Group
                        </h4>
                        <div class="chart-container">
                            <canvas id="questionGroupsBar"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Accuracy Radar Chart -->
            <div class="row">
                <div class="col-12">
                    <div class="chart-card">
                        <h4 style="text-align:center; margin-bottom: 1.5rem; color:#2c3e50; font-weight:600;">
                            <i class="fas fa-radar-chart" style="color: #9b59b6; margin-right: 0.5rem;"></i>
                            Accuracy Radar Analysis
                        </h4>
                        <div class="chart-container" style="height: 400px;">
                            <canvas id="questionGroupsRadar"></canvas>
                        </div>
                        <p style="text-align: center; color: #7f8c8d; font-size: 0.9rem; margin-top: 1rem;">
                            This radar chart shows your accuracy percentage across different question groups
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample data
        const questionGroupsData = {
            'PHP Basics': { total: 8, correct: 6 },
            'Advanced PHP': { total: 5, correct: 3 },
            'Database': { total: 7, correct: 5 }
        };

        const groupLabels = Object.keys(questionGroupsData);
        const groupTotals = groupLabels.map(k => questionGroupsData[k].total);
        const groupCorrect = groupLabels.map(k => questionGroupsData[k].correct);
        const groupAccuracy = groupLabels.map(k => {
            const total = questionGroupsData[k].total;
            const correct = questionGroupsData[k].correct;
            return total > 0 ? Math.round((correct / total) * 100) : 0;
        });

        const enhancedColors = [
            'rgba(52, 152, 219, 0.8)',
            'rgba(46, 204, 113, 0.8)',
            'rgba(155, 89, 182, 0.8)',
        ];

        const borderColors = [
            'rgba(52, 152, 219, 1)',
            'rgba(46, 204, 113, 1)',
            'rgba(155, 89, 182, 1)',
        ];

        // 1. Pie Chart
        new Chart(document.getElementById('questionGroupsPie').getContext('2d'), {
            type: 'doughnut',
            data: {
                labels: groupLabels,
                datasets: [{
                    data: groupTotals,
                    backgroundColor: enhancedColors,
                    borderColor: borderColors,
                    borderWidth: 3,
                    hoverOffset: 15
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                }
            }
        });

        // 2. Bar Chart
        new Chart(document.getElementById('questionGroupsBar').getContext('2d'), {
            type: 'bar',
            data: {
                labels: groupLabels,
                datasets: [
                    {
                        label: 'Total Questions',
                        data: groupTotals,
                        backgroundColor: 'rgba(52, 152, 219, 0.6)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 2,
                    },
                    {
                        label: 'Correct Answers',
                        data: groupCorrect,
                        backgroundColor: 'rgba(46, 204, 113, 0.8)',
                        borderColor: 'rgba(46, 204, 113, 1)',
                        borderWidth: 2,
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'top' }
                },
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });

        // 3. Radar Chart
        new Chart(document.getElementById('questionGroupsRadar').getContext('2d'), {
            type: 'radar',
            data: {
                labels: groupLabels,
                datasets: [{
                    label: 'Accuracy %',
                    data: groupAccuracy,
                    backgroundColor: 'rgba(155, 89, 182, 0.2)',
                    borderColor: 'rgba(155, 89, 182, 1)',
                    borderWidth: 3,
                    pointBackgroundColor: 'rgba(155, 89, 182, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: { stepSize: 20 }
                    }
                }
            }
        });

        // Create custom legend for pie chart
        const legendContainer = document.getElementById('pieChartLegend');
        let legendHTML = '';
        groupLabels.forEach((label, index) => {
            const color = enhancedColors[index];
            const total = groupTotals[index];
            const percentage = ((total / groupTotals.reduce((a, b) => a + b, 0)) * 100).toFixed(1);
            legendHTML += `
                <div style="display: inline-block; margin: 0.25rem 0.5rem; font-size: 0.9rem;">
                    <span style="display: inline-block; width: 12px; height: 12px; background-color: ${color}; border-radius: 50%; margin-right: 0.5rem;"></span>
                    <strong>${label}</strong>: ${total} (${percentage}%)
                </div>
            `;
        });
        legendContainer.innerHTML = legendHTML;
    </script>
</body>
</html>
