<div>
    <!-- Enhanced Quiz Result Styles -->
    <style>
        .quiz-result-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .result-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .result-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .result-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .result-header h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 2;
        }

        .result-status {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        /* Simple Chart Container Styles */
        .chart-container {
            position: relative;
            height: 400px;
        }

        .result-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        .pass-note { opacity: 0.85; font-style: italic; margin-top: .25rem; }


            /* Reveal-on-scroll animation */
            .reveal { opacity: 0; transform: translateY(12px); transition: opacity .5s ease, transform .5s ease; }
            .reveal.in-view { opacity: 1; transform: translateY(0); }

        .success-badge {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            display: inline-block;
            margin-bottom: 1rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(17, 153, 142, 0.3);
        }

        .failed-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            display: inline-block;
            margin-bottom: 1rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 2rem 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            padding: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(240, 147, 251, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        }

        .stat-card.correct {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            box-shadow: 0 10px 25px rgba(17, 153, 142, 0.3);
        }

        .stat-card.correct:hover {
            box-shadow: 0 15px 35px rgba(17, 153, 142, 0.4);
        }

        .stat-card.wrong {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
        }

        .stat-card.wrong:hover {
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .action-buttons {
            padding: 2rem;
            text-align: center;
            background: #f8f9fa;
        }

        .btn-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-secondary-modern {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
            box-shadow: 0 4px 15px rgba(252, 182, 159, 0.3);
        }

        .btn-secondary-modern:hover {
            box-shadow: 0 8px 25px rgba(252, 182, 159, 0.4);
            color: #333;
        }

        .progress-ring {
            width: 200px;
            height: 200px;
            margin: 0 auto 2rem;
        }

        .progress-ring-circle {
            stroke: #e6e6e6;
            stroke-width: 8;
            fill: transparent;
            r: 90;
            cx: 100;
            cy: 100;
        }

        .progress-ring-progress {
            stroke: url(#gradient);
            stroke-width: 8;
            stroke-linecap: round;
            fill: transparent;
            r: 90;
            cx: 100;
            cy: 100;
            stroke-dasharray: 565.48;
            stroke-dashoffset: 565.48;
            transform: rotate(-90deg);
            transform-origin: 100px 100px;
            transition: stroke-dashoffset 2s ease-in-out;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2rem;
            font-weight: 700;
            color: #333;
        }

        @media (max-width: 768px) {
            .result-header h3 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                padding: 1rem;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .chart-container {
                height: 250px;
            }
        }
    </style>

    <div class="quiz-result-modern">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-xl-10">
                    <div class="result-card">
                        <!-- Enhanced Result Header -->
                        <div class="result-header">
                            <h3>{{$course->quiz->title}}</h3>

                            @if ($quiz->publish==1)
                                @if($result['pass'])
                                    <div class="success-badge">
                                        <i class="fas fa-trophy"></i> {{__('student.Congratulations!')}}
                                    </div>
                                @else
                                    <div class="failed-badge">
                                        <i class="fas fa-times-circle"></i> Keep Going - You're Almost There!
                                    </div>
                                @endif
                            @else
                                <div class="success-badge">
                                    <i class="fas fa-clock"></i> {{__('quiz.Please wait till completion marking process')}}
                                </div>
                            @endif

                            <p class="result-subtitle">You have completed {{$course->quiz->title}}</p>
                            <div class="pass-note">Minimum Passing Score: {{$result['passMark']}}%</div>
                        </div>

                        @if ($quiz->publish==1)
                            <!-- Modern Statistics Display -->
                            <div class="stats-grid">
                                <div class="stat-card correct reveal">
                                    <span class="stat-number">{{$result['totalCorrect']}}</span>
                                    <span class="stat-label">Correct Answers</span>
                                </div>

                                <div class="stat-card wrong reveal">
                                    <span class="stat-number">{{$result['totalWrong']}}</span>
                                    <span class="stat-label">Wrong Answers</span>
                                </div>

                                <div class="stat-card reveal">
                                    <span class="stat-number">{{$result['score']}}/{{$result['totalScore']}}</span>
                                    <span class="stat-label">Score</span>
                                </div>

                                <div class="stat-card reveal">
                                    <span class="stat-number">{{$result['mark']}}%</span>
                                    <span class="stat-label">Percentage</span>
                                </div>
                            </div>

                            <!-- Interactive Charts Section -->
                            <div style="padding: 2rem; background: white;">
                                <div class="row">
                                    <!-- Circular Progress Chart -->
                                    <div class="col-md-6">
                                        <h4 style="text-align: center; margin-bottom: 2rem; color: #333; font-weight: 600;">Performance Overview</h4>
                                        <div style="position: relative; display: flex; justify-content: center;">
                                            <svg class="progress-ring" width="200" height="200">
                                                <defs>
                                                    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                        <stop offset="0%" style="stop-color:#11998e;stop-opacity:1" />
                                                        <stop offset="100%" style="stop-color:#38ef7d;stop-opacity:1" />
                                                    </linearGradient>
                                                </defs>
                                                <circle class="progress-ring-circle" />
                                                <circle class="progress-ring-progress" id="progressCircle" />
                                            </svg>
                                            <div class="progress-text">{{$result['mark']}}%</div>
                                        </div>
                                    </div>

                                    <!-- Bar Chart -->
                                    <div class="col-md-6">

                                    <!-- Simple Question Groups Chart -->
                                    @if(!empty($result['difficultyBreakdown']))
                                        <div class="row mt-4">
                                            <div class="col-12">
                                                <h4 style="text-align:center; margin-bottom: 1.5rem; color:#2c3e50; font-weight:600;">
                                                    <i class="fas fa-chart-bar" style="color: #3498db; margin-right: 0.5rem;"></i>
                                                    Question Groups Distribution
                                                </h4>
                                                <div class="chart-container" style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 5px 20px rgba(0,0,0,0.1);">
                                                    <canvas id="questionGroupsChart"></canvas>
                                                </div>

                                                <!-- Group Summary -->
                                                <div class="mt-3 text-center">
                                                    @foreach($result['difficultyBreakdown'] as $groupName => $groupData)
                                                        <span style="display: inline-block; margin: 0.5rem; padding: 0.5rem 1rem; background: #f8f9fa; border-radius: 20px; font-size: 0.9rem;">
                                                            <strong>{{ $groupName }}</strong>: {{ $groupData['correct'] ?? 0 }}/{{ $groupData['total'] ?? 0 }}
                                                            ({{ $groupData['total'] > 0 ? round(($groupData['correct'] / $groupData['total']) * 100, 1) : 0 }}%)
                                                        </span>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>


                                    <!-- Quick glance charts: Answer Pie -->
                                    <div class="row mt-4">
                                        <div class="col-md-6">
                                            <h4 style="text-align:center; margin-bottom: 1rem; color:#333; font-weight:600;">Answer Overview</h4>
                                            <div class="chart-container">
                                                <canvas id="answerPie"></canvas>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h4 style="text-align:center; margin-bottom: 1rem; color:#333; font-weight:600;">Legacy Difficulty Chart</h4>
                                            <div class="chart-container">
                                                <canvas id="difficultyChart"></canvas>
                                            </div>
                                        </div>
                                    </div>

                                        <h4 style="text-align: center; margin-bottom: 2rem; color: #333; font-weight: 600;">Answer Breakdown</h4>
                                        <div class="chart-container">
                                            <canvas id="quizResultChart"></canvas>
                                        </div>
                                    </div>
                                </div>

                                <!-- Doughnut Chart for Overall Performance -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h4 style="text-align: center; margin-bottom: 2rem; color: #333; font-weight: 600;">Score Overview</h4>
                                        <div class="chart-container" style="height: 400px;">
                                            <canvas id="performanceChart"></canvas>
                                        </div>
                                    </div>
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <h4 style="text-align:center; margin-bottom: 1rem; color:#333; font-weight:600;">Subject Overview</h4>
                                            <div class="chart-container">
                                                <canvas id="subjectChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Modern Action Buttons -->
                            <div class="action-buttons">
                                <a href="{{courseDetailsUrl(@$course->id,@$course->type,@$course->slug)}}"
                                   class="btn-modern">
                                    <i class="fas fa-check-circle"></i> {{__('student.Done')}}
                                </a>

                                @if(count($preResult)!=0)
                                    <button type="button" class="btn-secondary-modern showHistory">
                                        <i class="fas fa-history"></i> {{__('frontend.View History')}}
                                    </button>
                                @endif

                                <a href="{{$quiz->quiz->show_ans_sheet==1?route('quizResultPreview',$quiz->id):'#'}}"
                                   title="{{$quiz->quiz->show_ans_sheet!=1?__('quiz.Answer Sheet is currently locked by Teacher'):''}}"
                                   class="btn-secondary-modern submit_q_btn">
                                    <i class="fas fa-file-alt"></i> {{__('student.See Answer Sheet')}}
                                </a>
                            </div>
                        @endif

                        <!-- Enhanced History Section -->
                        @if(count($preResult)!=0)
                            <div id="historyDiv" style="display:none; padding: 2rem; background: #f8f9fa;">
                                <h4 style="margin-bottom: 1.5rem; color: #333; font-weight: 600;">
                                    <i class="fas fa-chart-line"></i> {{__('frontend.Quiz History')}}
                                </h4>
                                <div style="overflow-x: auto;">
                                    <table class="table" style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                                        <thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                            <tr>
                                                <th style="border: none; padding: 1rem;">{{__('frontend.Date')}}</th>
                                                <th style="border: none; padding: 1rem;">{{__('frontend.Mark')}}</th>
                                                <th style="border: none; padding: 1rem;">{{__('frontend.Percentage')}}</th>
                                                <th style="border: none; padding: 1rem;">{{__('frontend.Rating')}}</th>
                                                @if($quiz->quiz->show_result_each_submit==1)
                                                    <th style="border: none; padding: 1rem;">{{__('frontend.Details')}}</th>
                                                @endif
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($preResult as $pre)
                                                <tr style="transition: background-color 0.3s ease;">
                                                    <td style="padding: 1rem; border: none; border-bottom: 1px solid #eee;">{{$pre['date']}}</td>
                                                    <td style="padding: 1rem; border: none; border-bottom: 1px solid #eee; font-weight: 600;">{{$pre['score']}}/{{$pre['totalScore']}}</td>
                                                    <td style="padding: 1rem; border: none; border-bottom: 1px solid #eee;">
                                                        <span style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.9rem; font-weight: 600;">
                                                            {{$pre['mark']}}%
                                                        </span>
                                                    </td>
                                                    <td style="padding: 1rem; border: none; border-bottom: 1px solid #eee;">
                                                        <span class="{{$pre['text_color'] == 'success_text' ? 'success-badge' : 'failed-badge'}}" style="font-size: 0.9rem;">
                                                            {{$pre['status']}}
                                                        </span>
                                                    </td>
                                                    @if($quiz->quiz->show_result_each_submit==1)
                                                        <td style="padding: 1rem; border: none; border-bottom: 1px solid #eee;">
                                                            <a href="{{route('quizResultPreview',$pre['quiz_test_id'])}}"
                                                               class="btn-modern" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                                                                <i class="fas fa-eye"></i> {{__('student.See Answer Sheet')}}
                                                            </a>
                                                        </td>
                                                    @endif
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js and Animation Scripts -->
    <script src="{{asset('public/backend/vendors/chartlist/Chart.min.js')}}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Data from PHP (robust numeric parsing to avoid NaN)
            const correctAnswers = Number('{{ $result['totalCorrect'] ?? 0 }}') || 0;
            const wrongAnswers = Number('{{ $result['totalWrong'] ?? 0 }}') || 0;
            const percentage = Number('{{ $result['mark'] ?? 0 }}') || 0;
            const totalQuestions = correctAnswers + wrongAnswers;

            // Animate circular progress
            const circle = document.getElementById('progressCircle');
            const circumference = 2 * Math.PI * 90;
            const offset = circumference - (percentage / 100) * circumference;

            setTimeout(() => {
                circle.style.strokeDashoffset = offset;
            }, 500);

            // Bar Chart for Answer Breakdown
            const ctx1 = document.getElementById('quizResultChart').getContext('2d');
            new Chart(ctx1, {
                type: 'bar',
                data: {
                    labels: ['Correct', 'Wrong'],
                    datasets: [{
                        label: 'Number of Questions',
                        data: [correctAnswers, wrongAnswers],
                        backgroundColor: [
                            'rgba(17, 153, 142, 0.8)',
                            'rgba(255, 107, 107, 0.8)'
                        ],
                        borderColor: [
                            'rgba(17, 153, 142, 1)',
                            'rgba(255, 107, 107, 1)'
                        ],
                        borderWidth: 2,
                        borderRadius: 10,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: { display: false },
                    scales: {
                        yAxes: [{
                            ticks: { beginAtZero: true, stepSize: 1, min: 0 },
                            gridLines: { color: 'rgba(0,0,0,0.1)' }
                        }],
                        xAxes: [{
                            gridLines: { display: false }
                        }]
                    },
                    animation: {
                        duration: 800,
                        easing: 'easeOutCubic'
                    }
                }
            });

            // Doughnut Chart for Performance
            const ctx2 = document.getElementById('performanceChart').getContext('2d');
            new Chart(ctx2, {
                type: 'doughnut',
                data: {
                    labels: ['Correct', 'Wrong', 'Remaining'],
                    datasets: [{
                        data: [correctAnswers, wrongAnswers, Math.max(0, 100 - percentage)],
                        backgroundColor: [
                            'rgba(17, 153, 142, 0.8)',
                            'rgba(255, 107, 107, 0.8)',
                            'rgba(200, 200, 200, 0.3)'
                        ],
                        borderColor: [
                            'rgba(17, 153, 142, 1)',
                            'rgba(255, 107, 107, 1)',
                            'rgba(200, 200, 200, 0.5)'
                        ],
                        borderWidth: 3,
                        hoverOffset: 10
                    }]

                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    size: 14,
                                    weight: '600'
                                }
                            }
                        }
                    },
                    animation: {
                        animateRotate: true,
                        duration: 2000
                    }
                }
            });

	            // Reveal-on-scroll animations
	            const revealObserver = new IntersectionObserver((entries) => {
	                entries.forEach(e => { if (e.isIntersecting) e.target.classList.add('in-view'); });
	            }, { threshold: 0.15 });
	            document.querySelectorAll('.reveal, .stat-card, .action-buttons, .chart-container')
	                .forEach(el => revealObserver.observe(el));

	            // Answer Pie chart (correct vs wrong)
	            const answerPieEl = document.getElementById('answerPie');
	            if (answerPieEl) {
	                new Chart(answerPieEl.getContext('2d'), {
	                    type: 'pie',
	                    data: {
	                        labels: ['Correct','Wrong'],
	                        datasets: [{
	                            data: [correctAnswers, wrongAnswers],
	                            backgroundColor: ['rgba(17,153,142,.9)','rgba(255,107,107,.9)'],
	                            borderColor: ['rgba(17,153,142,1)','rgba(255,107,107,1)'],
	                            borderWidth: 2
	                        }]
	                    },
	                    options: {
	                        responsive: true,
	                        maintainAspectRatio: false,
	                        plugins: { legend: { position: 'bottom' } },
	                        animation: { animateScale: true, animateRotate: true, duration: 1200 }
	                    }
	                });
	            }

	            // Category Radar chart (placeholder distribution)
	            const radarEl = document.getElementById('categoryRadar');
	            if (radarEl) {
	                const categories = ['Section A','Section B','Section C','Section D'];
	                const per = percentage;
	                const pts = [per*.4, per*.3, per*.2, per*.1].map(v=> Math.round(v));
	                new Chart(radarEl.getContext('2d'), {
	                    type: 'radar',
	                    data: {
	                        labels: categories,
	                        datasets: [{
	                            label: 'Performance by Section',
	                            data: pts,
	                            backgroundColor: 'rgba(102,126,234,0.2)',
	                            borderColor: 'rgba(102,126,234,1)',
	                            pointBackgroundColor: 'rgba(118,75,162,1)',
	                            borderWidth: 2
	                        }]
	                    },
	                    options: {
	                        responsive: true,
	                        maintainAspectRatio: false,
	                        scales: { r: { suggestedMin: 0, suggestedMax: 100, angleLines: { color: 'rgba(0,0,0,.05)'} } },
	                        plugins: { legend: { display: false } },
	                        animation: { duration: 1200, easing: 'easeOutQuart' }
	                    }
	                });
	            }
                    console.log('raw result object', @json($result));

                    // Simple Question Groups Chart
                    const questionGroupsData = @json($result['difficultyBreakdown'] ?? []);
                    console.log('Question Groups Data:', questionGroupsData);

                    if (Object.keys(questionGroupsData).length > 0) {
                        const groupLabels = Object.keys(questionGroupsData);
                        const groupTotals = groupLabels.map(k => Number(questionGroupsData[k]?.total || 0));
                        const groupCorrect = groupLabels.map(k => Number(questionGroupsData[k]?.correct || 0));

                        // Simple color palette
                        const colors = [
                            'rgba(52, 152, 219, 0.8)',   // Blue
                            'rgba(46, 204, 113, 0.8)',   // Green
                            'rgba(155, 89, 182, 0.8)',   // Purple
                            'rgba(241, 196, 15, 0.8)',   // Yellow
                            'rgba(231, 76, 60, 0.8)',    // Red
                            'rgba(26, 188, 156, 0.8)',   // Teal
                            'rgba(230, 126, 34, 0.8)',   // Orange
                            'rgba(149, 165, 166, 0.8)',  // Gray
                        ];

                        const borderColors = colors.map(color => color.replace('0.8', '1'));

                        // Simple Question Groups Bar Chart
                        const chartCtx = document.getElementById('questionGroupsChart');
                        if (chartCtx) {
                            new Chart(chartCtx.getContext('2d'), {
                                type: 'bar',
                                data: {
                                    labels: groupLabels,
                                    datasets: [
                                        {
                                            label: 'Total Questions',
                                            data: groupTotals,
                                            backgroundColor: colors.slice(0, groupLabels.length).map(c => c.replace('0.8', '0.6')),
                                            borderColor: borderColors.slice(0, groupLabels.length),
                                            borderWidth: 2,
                                            borderRadius: 8,
                                        },
                                        {
                                            label: 'Correct Answers',
                                            data: groupCorrect,
                                            backgroundColor: colors.slice(0, groupLabels.length),
                                            borderColor: borderColors.slice(0, groupLabels.length),
                                            borderWidth: 2,
                                            borderRadius: 8,
                                        }
                                    ]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    plugins: {
                                        legend: {
                                            position: 'top',
                                            labels: {
                                                usePointStyle: true,
                                                padding: 20,
                                                font: { size: 12, weight: '600' }
                                            }
                                        },
                                        tooltip: {
                                            callbacks: {
                                                afterLabel: function(context) {
                                                    if (context.datasetIndex === 1) {
                                                        const total = groupTotals[context.dataIndex];
                                                        const correct = context.parsed.y;
                                                        const accuracy = total > 0 ? ((correct / total) * 100).toFixed(1) : 0;
                                                        return `Accuracy: ${accuracy}%`;
                                                    }
                                                    return '';
                                                }
                                            }
                                        }
                                    },
                                    scales: {
                                        y: {
                                            beginAtZero: true,
                                            ticks: { stepSize: 1, font: { size: 11 } },
                                            grid: { color: 'rgba(0,0,0,0.1)' }
                                        },
                                        x: {
                                            ticks: { font: { size: 11 } },
                                            grid: { display: false }
                                        }
                                    },
                                    animation: { duration: 1200, easing: 'easeOutQuart' }
                                }
                            });
                        }
                    }

                    // Legacy Difficulty chart (keeping for backward compatibility)
                    const diffEl = document.getElementById('difficultyChart');
                    if (diffEl && Object.keys(questionGroupsData).length > 0) {
                        const dLabels = Object.keys(questionGroupsData);
                        const dCorrect = dLabels.map(k => Number(questionGroupsData[k]?.total || 0));
                        const dColors = dLabels.map(l => {
                            const t = String(l).toLowerCase();
                            if (t.includes('beginner') || t.includes('easy')) return 'rgba(46,204,113,0.8)';
                            if (t.includes('intermediate') || t.includes('medium')) return 'rgba(255,193,7,0.8)';
                            if (t.includes('advance') || t.includes('advanced') || t.includes('hard')) return 'rgba(231,76,60,0.8)';
                            if (t.includes('pro')) return 'rgba(52,152,219,0.8)';
                            return 'rgba(99,110,114,0.8)';
                        });

                        new Chart(diffEl.getContext('2d'), {
                            type: 'bar',
                            data: {
                                labels: dLabels,
                                datasets: [{
                                    label: 'Questions',
                                    data: dCorrect,
                                    backgroundColor: dColors,
                                    borderColor: dColors.map(c => c.replace('0.8', '1')),
                                    borderWidth: 2,
                                    borderRadius: 6
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: { display: false }
                                },
                                scales: {
                                    y: [{
                                        ticks: { beginAtZero: true, stepSize: 1 },
                                        grid: { color: 'rgba(0,0,0,0.1)' }
                                    }],
                                    x: [{
                                        grid: { display: false }
                                    }]
                                },
                                animation: {
                                    duration: 800,
                                    easing: 'easeOutCubic'
                                }
                            }
                        });
                    }

                    // Subject chart from quiz-level subcategory
                    const subj = @json($result['subjectBreakdown'] ?? []);
                    console.log('subject breakdown', subj);
                    const subjectEl = document.getElementById('subjectChart');
                    if (subjectEl) {
                        const sLabels = Object.keys(subj || {});
                        const sTotals = sLabels.map(k => Number(subj[k]?.total || 0));
                        const sCorrect = sLabels.map(k => Number(subj[k]?.correct || 0));
                        if (sLabels.length === 0) {
                            subjectEl.parentNode.insertAdjacentHTML('beforeend', '<div style="text-align:center;color:#888;">No subject data</div>');
                        } else {
                            new Chart(subjectEl.getContext('2d'), {
                                type: 'bar',
                                data: { labels: sLabels, datasets: [
                                    { label: 'Total', data: sTotals, backgroundColor: 'rgba(52,152,219,0.3)', borderColor: 'rgba(52,152,219,1)', borderWidth: 2 },
                                    { label: 'Correct', data: sCorrect, backgroundColor: 'rgba(46,204,113,0.8)', borderColor: 'rgba(46,204,113,1)', borderWidth: 2 }
                                ]},
                                options: { responsive: true, legend: { position: 'bottom' }, scales: { yAxes: [{ ticks: { beginAtZero: true, stepSize: 1 } }] } }
                            });
                        }
                    }



            // Show/Hide History
            document.querySelector('.showHistory')?.addEventListener('click', function() {
                const historyDiv = document.getElementById('historyDiv');
                if (historyDiv.style.display === 'none') {
                    historyDiv.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-eye-slash"></i> {{__("frontend.Hide History")}}';
                } else {
                    historyDiv.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-history"></i> {{__("frontend.View History")}}';
                }
            });

            // Add hover effects to table rows
            document.querySelectorAll('#historyDiv tbody tr').forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f8f9fa';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                });
            });
        });
    </script>
</div>


