<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Question Groups Chart - Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .chart-container {
            position: relative;
            height: 400px;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .group-summary {
            margin-top: 1rem;
            text-align: center;
        }

        .group-summary span {
            display: inline-block;
            margin: 0.5rem;
            padding: 0.5rem 1rem;
            background: #f8f9fa;
            border-radius: 20px;
            font-size: 0.9rem;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 style="color: #2c3e50; font-weight: 700;">
                <i class="fas fa-chart-bar" style="color: #3498db; margin-right: 0.5rem;"></i>
                Simple Question Groups Chart
            </h1>
            <p style="color: #7f8c8d; font-size: 1.1rem;">
                This is the simplified chart that will appear on your Quiz Result pages
            </p>
        </div>

        <!-- Simple Question Groups Chart -->
        <div class="row mt-4">
            <div class="col-12">
                <h4 style="text-align:center; margin-bottom: 1.5rem; color:#2c3e50; font-weight:600;">
                    <i class="fas fa-chart-bar" style="color: #3498db; margin-right: 0.5rem;"></i>
                    Question Groups Distribution
                </h4>
                <div class="chart-container">
                    <canvas id="questionGroupsChart"></canvas>
                </div>
                
                <!-- Group Summary -->
                <div class="group-summary">
                    <span><strong>PHP Basics</strong>: 6/8 (75.0%)</span>
                    <span><strong>Advanced PHP</strong>: 3/5 (60.0%)</span>
                    <span><strong>Database</strong>: 5/7 (71.4%)</span>
                </div>
            </div>
        </div>

        <div class="mt-4 text-center">
            <div class="alert alert-success" role="alert">
                <h5><i class="fas fa-check-circle"></i> Implementation Complete!</h5>
                <p class="mb-0">This simple chart will automatically appear on quiz result pages when questions are assigned to different Question Groups.</p>
            </div>
        </div>
    </div>

    <script>
        // Sample data matching your implementation
        const questionGroupsData = {
            'PHP Basics': { total: 8, correct: 6 },
            'Advanced PHP': { total: 5, correct: 3 },
            'Database': { total: 7, correct: 5 }
        };

        const groupLabels = Object.keys(questionGroupsData);
        const groupTotals = groupLabels.map(k => questionGroupsData[k].total);
        const groupCorrect = groupLabels.map(k => questionGroupsData[k].correct);

        // Simple color palette (matching your implementation)
        const colors = [
            'rgba(52, 152, 219, 0.8)',   // Blue
            'rgba(46, 204, 113, 0.8)',   // Green
            'rgba(155, 89, 182, 0.8)',   // Purple
        ];

        const borderColors = colors.map(color => color.replace('0.8', '1'));

        // Simple Question Groups Bar Chart (exactly like your implementation)
        const chartCtx = document.getElementById('questionGroupsChart');
        if (chartCtx) {
            new Chart(chartCtx.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: groupLabels,
                    datasets: [
                        {
                            label: 'Total Questions',
                            data: groupTotals,
                            backgroundColor: colors.map(c => c.replace('0.8', '0.6')),
                            borderColor: borderColors,
                            borderWidth: 2,
                            borderRadius: 8,
                        },
                        {
                            label: 'Correct Answers',
                            data: groupCorrect,
                            backgroundColor: colors,
                            borderColor: borderColors,
                            borderWidth: 2,
                            borderRadius: 8,
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: { size: 12, weight: '600' }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                afterLabel: function(context) {
                                    if (context.datasetIndex === 1) {
                                        const total = groupTotals[context.dataIndex];
                                        const correct = context.parsed.y;
                                        const accuracy = total > 0 ? ((correct / total) * 100).toFixed(1) : 0;
                                        return `Accuracy: ${accuracy}%`;
                                    }
                                    return '';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: { stepSize: 1, font: { size: 11 } },
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        x: {
                            ticks: { font: { size: 11 } },
                            grid: { display: false }
                        }
                    },
                    animation: { duration: 1200, easing: 'easeOutQuart' }
                }
            });
        }
    </script>
</body>
</html>
